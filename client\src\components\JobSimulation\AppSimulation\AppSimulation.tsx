import { useEffect, useRef, useState } from 'react';
import { useSetRecoilState } from 'recoil';
import {
  AppSimulationElementDropdownOption,
  AppSimulationModalConfig,
  AppSimulationScreen,
  AppSimulationScreenPlaceholder,
} from '~/common';
import { Tag } from '~/components';
import {
  createCalculationEngine,
  findScreenIndexById,
  formatValueWithType,
  getElementStyles,
  getPlaceholderStyle,
  hasTimeBasedPlaceholders,
  safeCalculateEstimates,
  uploadAppSimulationPhoto,
} from '~/components/JobSimulation/AppSimulation/AppSimulationHelpers';
import { CalculationConfig } from '~/components/JobSimulation/AppSimulation/CalculationEngine';
import DialogTextInput from '~/components/JobSimulation/AppSimulation/DialogTextInput';
import LineChart from '~/components/JobSimulation/AppSimulation/LineChart';
import store from '~/store';
import { cn } from '~/utils';
import AppSimulationDropdown from './AppSimulationDropdown';
import DialogDynamicInputs from './DialogDynamicInputs';
import PlaceholderImage from './Placeholder/PlaceholderImage';

interface AppSimulationProps {
  appSimulationScreens: AppSimulationScreen[];
  appSimulationConfig: CalculationConfig;
}

const AppSimulation = ({ appSimulationScreens, appSimulationConfig }: AppSimulationProps) => {
  console.log('::: AppSimulation :::');
  const setJobsimulationTriggerMessage = useSetRecoilState(store.jobSimulationTriggerMessage);

  const imageRef: React.MutableRefObject<HTMLImageElement | null> = useRef(null);
  const containerRef: React.MutableRefObject<HTMLDivElement | null> = useRef(null);
  const imageContainerRef = useRef<HTMLDivElement | null>(null);

  const [currentScreenIndex, setCurrentScreenIndex] = useState(0);
  const [previousSreenIndex, setPreviousScreenIndex] = useState(0);
  const [showDropdown, setShowDropdown] = useState(false);
  const [dropdownOptions, setDropdownOptions] = useState<Array<AppSimulationElementDropdownOption>>(
    [],
  );
  const [dropdownPosition, setDropdownPosition] = useState({ x: 0, y: 0 });
  // Use in message
  const [userSelections, setUserSelections] = useState<Record<string, string>>({});

  // State for data content. Use for displaying in placeholders
  const [dataContent, setDataContent] = useState<
    Record<
      string,
      {
        originalValue?: any;
        value?: any;
        valueType?: 'number' | 'string' | 'imgPath';
      }
    >
  >({});

  const [hoveredElements, setHoveredElements] = useState<number[]>([]);

  // State for text input dialog
  const [showTextDialog, setShowTextDialog] = useState(false);
  const [textInputData, setTextInputData] = useState<{
    type: string;
    value: string;
    dataContextId: string;
    saveToSelections: boolean;
    dataContextLabel: string;
  }>({
    type: 'input',
    value: '',
    dataContextId: '',
    saveToSelections: false,
    dataContextLabel: '',
  });

  // State for dynamic modal
  const [showModal, setShowModal] = useState(false);
  const [currentModalConfig, setCurrentModalConfig] = useState<AppSimulationModalConfig | null>(
    null,
  );
  const [modalValues, setModalValues] = useState<Record<string, any>>({});

  // State for time-based placeholders
  // const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime, setStartTime] = useState<number | null>(null);
  const [timeBasedValues, setTimeBasedValues] = useState<Record<string, string>>({});
  const [lastUpdateTimes, setLastUpdateTimes] = useState<Record<string, number>>({});

  // Initialize Calculation Engine dynamically
  const [calculationEngine] = useState(() => {
    return createCalculationEngine(appSimulationConfig);
  });

  // Getefault values for calculation config
  const [dynamicSelections, setDynamicSelections] = useState<Record<string, any>>(() => {
    return calculationEngine?.getDefaultValues() || {};
  });
  const [calculatedEstimates, setCalculatedEstimates] = useState<Record<string, string>>({});

  // State for responsive font scaling
  const [imageScaleFactor, setImageScaleFactor] = useState<number>(1);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  // Calculate parameters change using Calculation Engine
  useEffect(() => {
    // Only skip if dynamicSelections is null/undefined, not if it's empty object
    if (!dynamicSelections || !calculationEngine || !calculationEngine.getDefaultValues()) return;

    const estimates = safeCalculateEstimates(calculationEngine, dynamicSelections);
    setCalculatedEstimates(estimates);
  }, [dynamicSelections, calculationEngine]);

  useEffect(() => {
    setShowDropdown(false);

    if (appSimulationScreens[currentScreenIndex].actions?.length) {
      for (const action of appSimulationScreens[currentScreenIndex].actions) {
        if (action.type === 'triggerMessage') {
          let finalMessage = action.message || '';

          if (action.withData && Object.keys(userSelections).length > 0) {
            const selectionsText = Object.entries(userSelections)
              .map(([key, value]) => `${key}: ${value}`)
              .join(', ');
            finalMessage = `${finalMessage} ${selectionsText}`;
            console.log('::: finalMessage ::: ', finalMessage);
            setJobsimulationTriggerMessage({
              message: finalMessage,
              isTriggered: true,
            });
          }

          break;
        }
      }
    }

    // Init timer for screens have time-based placeholders
    if (hasTimeBasedPlaceholders(appSimulationScreens[currentScreenIndex])) {
      setStartTime(Date.now());
      // setElapsedTime(0);
      setLastUpdateTimes({});
      // Initialize time-based values
      const initialValues: Record<string, string> = {};
      appSimulationScreens[currentScreenIndex].placeholders?.forEach((placeholder) => {
        if (placeholder.dataByTime) {
          // Find initial value (time = 0)
          const initialData = placeholder.dataByTime.find((data) => data.time === 0);
          if (placeholder.dataId && dataContent[placeholder.dataId]?.value) {
            initialValues[placeholder.id] = dataContent[placeholder.dataId]?.value!;
          } else if (initialData) {
            initialValues[placeholder.id] = initialData.value;
          }
        } else if (placeholder.increaseByTime && placeholder.increaseFrom !== undefined) {
          if (placeholder.dataId && dataContent[placeholder.dataId]?.value) {
            initialValues[placeholder.id] = dataContent[placeholder.dataId]?.value!;
          } else {
            initialValues[placeholder.id] = placeholder.increaseFrom.toString();
          }
        }
      });
      setTimeBasedValues(initialValues);
    }
  }, [currentScreenIndex]);

  // Timer for time-based placeholders
  useEffect(() => {
    if (startTime === null || !hasTimeBasedPlaceholders(appSimulationScreens[currentScreenIndex]))
      return;

    const interval = setInterval(() => {
      const currentTime = Date.now();
      const elapsed = Math.floor((currentTime - startTime) / 1000); // Convert to seconds
      // setElapsedTime(elapsed);

      // Update time-based values
      const updatedValues: Record<string, string> = {};
      appSimulationScreens[currentScreenIndex].placeholders?.forEach((placeholder) => {
        if (placeholder.dataByTime) {
          // Find the appropriate value for current elapsed time
          let currentValue = '0';
          for (let i = placeholder.dataByTime.length - 1; i >= 0; i--) {
            if (elapsed >= placeholder.dataByTime[i].time) {
              currentValue = placeholder.dataByTime[i].value;
              break;
            }
          }
          updatedValues[placeholder.id] = currentValue;
        } else if (placeholder.increaseByTime && placeholder.increaseFrom !== undefined) {
          // Update every 10 seconds with random increase
          const currentVal = parseInt(
            timeBasedValues[placeholder.id] || placeholder.increaseFrom.toString(),
          );
          const lastUpdate = lastUpdateTimes[placeholder.id] || 0;
          const currentInterval = Math.floor(elapsed / 10);
          const lastInterval = Math.floor(lastUpdate / 10);

          if (elapsed >= 10 && currentInterval > lastInterval) {
            // Time for an update - add random increase
            const randomIncrease = Math.floor(Math.random() * 10) + 1;
            const newValue = currentVal + randomIncrease;
            updatedValues[placeholder.id] = newValue.toString();
            if (placeholder.dataId) {
              setDataContent((prev) => ({
                ...prev,
                [placeholder.dataId!]: {
                  ...prev[placeholder.dataId!],
                  value: newValue.toString(),
                },
              }));
            }
            console.log(
              `Updating ${placeholder.id}: ${currentVal} + ${randomIncrease} = ${newValue} at ${elapsed}s`,
            );
            // Update last update time
            setLastUpdateTimes((prev) => ({
              ...prev,
              [placeholder.id]: elapsed,
            }));
          } else {
            // Keep current value
            updatedValues[placeholder.id] = currentVal.toString();
            if (placeholder.dataId) {
              setDataContent((prev) => ({
                ...prev,
                [placeholder.dataId!]: {
                  ...prev[placeholder.dataId!],
                  value: currentVal.toString(),
                },
              }));
            }
          }
        }
      });

      setTimeBasedValues((prev) => ({ ...prev, ...updatedValues }));
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, [startTime, currentScreenIndex, timeBasedValues]);

  // Handle work portal resize --> image resize and calculate scale factor
  useEffect(() => {
    function calculateImageScale() {
      console.log('calculateImageScale ::: 1 ::: ');
      if (imageRef.current && containerRef.current) {
        console.log('calculateImageScale ::: 2 ::: ');
        const containerHeight = containerRef.current.clientHeight ?? 0;
        const naturalHeight = imageRef.current.naturalHeight || 1;

        // Set image styles
        imageRef.current.style.width = `auto`;
        imageRef.current.style.maxHeight = `${containerHeight}px`;

        if (imageContainerRef.current) {
          imageContainerRef.current.style.width = `auto`;
          imageContainerRef.current.style.maxHeight = `${containerHeight}px`;
        }

        // Calculate scale factor after image is loaded
        const actualHeight = imageRef.current.clientHeight || naturalHeight;
        const scaleFactor = actualHeight / naturalHeight;
        setImageScaleFactor(Math.max(0.3, Math.min(2, scaleFactor))); // Clamp between 0.3 and 2
      }
    }

    // Setup ResizeObserver for better performance
    if (containerRef.current && !resizeObserverRef.current) {
      resizeObserverRef.current = new ResizeObserver(() => {
        // Use requestAnimationFrame to avoid layout thrashing
        requestAnimationFrame(calculateImageScale);
      });
      resizeObserverRef.current.observe(containerRef.current);
    }

    // Initial calculation
    console.log('calculateImageScale ::: call init with screen ::: ', currentScreenIndex);
    calculateImageScale();

    // Cleanup function
    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
        resizeObserverRef.current = null;
      }
    };
  }, [imageRef.current?.src, currentScreenIndex]); // Removed problematic dependencies

  // Cleanup ResizeObserver on component unmount
  useEffect(() => {
    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
        resizeObserverRef.current = null;
      }
    };
  }, []);

  // Change image src when screen change
  useEffect(() => {
    if (imageRef.current) {
      imageRef.current.src = appSimulationScreens[currentScreenIndex].image;
    }
  }, [imageRef, currentScreenIndex]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (showDropdown) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showDropdown]);

  // Handle removing tag from placeholder
  const handleRemoveTag = (placeholderId: string, dataId: string, tagToRemove: string) => {
    const currentContent = dataContent[dataId];
    if (!currentContent?.value) return;

    const separator =
      appSimulationScreens[currentScreenIndex]?.placeholders?.find((p) => p.id === placeholderId)
        ?.dataSeparator || ', ';

    const currentTags = currentContent.value.split(separator).map((tag: string) => tag.trim());
    const updatedTags = currentTags.filter((tag: string) => tag !== tagToRemove);
    const updatedValue = updatedTags.join(separator);

    // Update dataContent
    setDataContent((prev) => ({
      ...prev,
      [dataId]: {
        ...prev[dataId],
        value: updatedValue,
      },
    }));

    // Update userSelections if needed
    setUserSelections((prev) => {
      const updatedSelections = { ...prev };
      Object.keys(updatedSelections).forEach((key) => {
        if (key === dataId) {
          updatedSelections[key] = `Languages: ${updatedValue}`;
        }
      });
      return updatedSelections;
    });

    // Update dynamic selections for calculation
    setDynamicSelections((prev) => ({
      ...prev,
      [dataId]: updatedTags,
    }));
  };

  // Render placeholder content based on display type
  const renderPlaceholderContent = (
    placeholder: AppSimulationScreenPlaceholder,
    displayText: string,
  ) => {
    if (placeholder.dataDisplayType === 'tag' && displayText) {
      const separator = placeholder.dataSeparator || ', ';
      const tags = displayText
        .split(separator)
        .map((tag) => tag.trim())
        .filter(Boolean);

      // Calculate responsive font size for tags
      const responsiveConfig = placeholder.responsiveConfig || {};
      const baseFontSize = responsiveConfig.baseFontSize || 0.75;
      const minFontSize = responsiveConfig.minFontSize || 0.5;
      const maxFontSize = responsiveConfig.maxFontSize || 1.2;
      const scaleWithImage = responsiveConfig.scaleWithImage !== false; // Default true

      let calculatedFontSize = baseFontSize;
      if (scaleWithImage) {
        calculatedFontSize = baseFontSize * imageScaleFactor;
        calculatedFontSize = Math.max(minFontSize, Math.min(maxFontSize, calculatedFontSize));
      }

      return (
        <div className="flex flex-wrap gap-1">
          {tags.map((tag, index) => (
            <Tag
              key={`${placeholder.id}-tag-${index}`}
              label={tag}
              onRemove={() => handleRemoveTag(placeholder.id, placeholder.dataId!, tag)}
              className="font-medium"
              labelClassName="p-1"
              style={{ fontSize: `${calculatedFontSize}rem` }}
            />
          ))}
        </div>
      );
    }

    // Default text display
    return displayText || '';
  };

  // Handle photo upload
  const handlePhotoUpload = (contextId: string) => {
    const callbackImage = (imgUrl: any) => {
      // Save image data by dataId instead of screenId
      setDataContent((prev) => ({
        ...prev,
        [contextId]: {
          ...prev[contextId],
          value: imgUrl,
        },
      }));
    };
    uploadAppSimulationPhoto({ callbackImage, width: 480, height: 480 });
  };

  // Handle text input
  const handleTextInput = (
    inputType: 'input' | 'textarea' = 'input',
    label?: string,
    dataContextId?: string,
    saveToSelections?: boolean,
  ) => {
    // Get current text value from dataContent using dataContextId
    setTextInputData({
      type: inputType,
      value: dataContent[dataContextId || 'unknown']?.value || '',
      dataContextId: dataContextId || '',
      saveToSelections: saveToSelections || false,
      dataContextLabel: label || 'Text Content',
    });
    setShowTextDialog(true);
  };

  // Handle text dialog submit
  const handleTextDialogSubmit = () => {
    if (textInputData?.saveToSelections && textInputData.dataContextId) {
      setDataContent((prev) => ({
        ...prev,
        [textInputData.dataContextId || 'unknown']: {
          ...prev[textInputData.dataContextId || 'unknown'],
          value: textInputData.value,
        },
      }));

      setUserSelections((prev) => ({
        ...prev,
        [textInputData.dataContextId || 'unknown']:
          `${textInputData.dataContextLabel || 'Text'}: "${textInputData.value}"`,
      }));
    }

    setShowTextDialog(false);
    setTextInputData({
      type: 'input',
      value: '',
      dataContextId: '',
      saveToSelections: false,
      dataContextLabel: '',
    });
  };

  // Handle checkbox action
  const handleCheckboxAction = (
    dataContextId: string,
    checkboxValue: string,
    dataContextLabel?: string,
    saveToSelections?: boolean,
  ) => {
    if (!dataContextId || !checkboxValue) return;

    // Get current array data
    const currentData = dataContent[dataContextId]?.value;
    let currentArray: string[] = [];

    if (Array.isArray(currentData)) {
      currentArray = [...currentData];
    } else if (typeof currentData === 'string' && currentData) {
      // .filter(Boolean) remove empty strings
      currentArray = currentData
        .split(',')
        .map((item) => item.trim())
        .filter(Boolean);
    }

    // Toggle checkbox value
    const isSelected = currentArray.includes(checkboxValue);
    let updatedArray: string[];

    if (isSelected) {
      // Case remove data
      updatedArray = currentArray.filter((item) => item !== checkboxValue);
    } else {
      // Case add data
      updatedArray = [...currentArray, checkboxValue];
    }

    // Update dataContent
    setDataContent((prev) => ({
      ...prev,
      [dataContextId]: {
        ...prev[dataContextId],
        value: updatedArray,
      },
    }));

    // Update userSelections if needed
    if (saveToSelections) {
      const formattedValue = updatedArray.join(', ');
      setUserSelections((prev) => ({
        ...prev,
        [dataContextId]: `${dataContextLabel || 'Selection'}: ${formattedValue}`,
      }));
    }

    // Update dynamic selections for calculation
    setDynamicSelections((prev) => ({
      ...prev,
      [dataContextId]: updatedArray,
    }));
  };

  // Handle dynamic modal submit
  const handleModalSubmit = () => {
    if (!currentModalConfig) return;

    console.log('Modal submit - current values:', modalValues);

    const updatedDataContent: Record<string, any> = {};
    const updatedUserSelections: Record<string, string> = {};

    currentModalConfig.inputs.forEach((input) => {
      const value = modalValues[input.id];
      if (value !== undefined && input.dataId) {
        let formattedValue = '';

        switch (input.type) {
          case 'multiSelect':
            formattedValue = Array.isArray(value) ? value.join(', ') : '';
            updatedUserSelections[input.dataId] = `${input.label}: ${formattedValue}`;
            // Update dynamic selections for calculation
            setDynamicSelections((prev) => ({
              ...prev,
              [input.dataId]: Array.isArray(value) ? value : [],
            }));
            break;
          case 'range':
            if (Array.isArray(value)) {
              formattedValue = formatValueWithType(value, input.formatType, input.formatConfig);
            }
            updatedUserSelections[input.dataId] = `${input.label}: ${formattedValue}`;
            // Update dynamic selections for calculation
            if (Array.isArray(value)) {
              setDynamicSelections((prev) => ({
                ...prev,
                [input.dataId]: [value[0], value[1]],
              }));
            }
            break;
          case 'radio':
          case 'text':
          case 'textarea':
            formattedValue = String(value);
            updatedUserSelections[input.dataId] = `${input.label}: ${formattedValue}`;
            // Update dynamic selections for calculation
            setDynamicSelections((prev) => ({
              ...prev,
              [input.dataId]: String(value),
            }));
            break;
          default:
            formattedValue = String(value);
            updatedUserSelections[input.dataId] = `${input.label}: ${formattedValue}`;
        }

        updatedDataContent[input.dataId] = {
          value: formattedValue,
        };
      }
    });

    // Update placeholders with selected data
    setDataContent((prev) => ({
      ...prev,
      ...updatedDataContent,
    }));

    // Update user selections
    setUserSelections((prev) => ({
      ...prev,
      ...updatedUserSelections,
    }));

    setShowModal(false);
    setCurrentModalConfig(null);
    setModalValues({});
  };

  const handleClickButton = (e: React.MouseEvent<HTMLImageElement>) => {
    if (!imageRef.current) return;

    const screenElements = appSimulationScreens[currentScreenIndex]?.elements || [];
    const indexes = screenElements.map((_, index) => index);
    setHoveredElements(indexes);
  };

  const handleDropdownOptionClick = (option: AppSimulationElementDropdownOption) => {
    const { screenId, dataContext, dataContextId, saveToSelections, value, label } = option;
    const targetIndex = findScreenIndexById(appSimulationScreens, screenId);
    if (targetIndex !== -1) {
      setPreviousScreenIndex(currentScreenIndex);
      setCurrentScreenIndex(targetIndex);
    }

    // Save user selection if saveToSelections is true
    if (saveToSelections && dataContextId) {
      setUserSelections((prev) => ({
        ...prev,
        [dataContextId]: dataContext || label,
      }));
    }

    // Update dynamic selections for calculation
    if (dataContextId && value !== undefined) {
      setDynamicSelections((prev) => ({
        ...prev,
        [dataContextId]: value,
      }));
    }

    setShowDropdown(false);
  };

  return (
    <>
      <div
        className={cn(
          'flex h-full flex-col items-center justify-center',
          appSimulationScreens[currentScreenIndex].bgColor
            ? `${appSimulationScreens[currentScreenIndex].bgColor}`
            : '',
        )}
        ref={containerRef}
      >
        <div className="relative" ref={imageContainerRef}>
          <img
            ref={imageRef}
            onClick={handleClickButton}
            // className="max-h-full w-auto"
            style={{ display: 'block' }} // Ensure image is block to avoid layout issues
          />

          {/* Overlay for buttons */}
          {appSimulationScreens[currentScreenIndex].elements?.map((element, index) => (
            <div
              key={`element-${appSimulationScreens[currentScreenIndex].id}-${index}`}
              style={getElementStyles(
                { ...element, index },
                { isHovered: hoveredElements.includes(index) },
              )}
              onMouseEnter={() => setHoveredElements([index])}
              onMouseLeave={() => setHoveredElements([])}
              onClick={(e) => {
                e.stopPropagation();
                const arrayButtonAction =
                  element.actions || (element.action ? [element.action] : []) || [];
                for (const buttonAction of arrayButtonAction) {
                  if (!buttonAction) continue;
                  if (buttonAction.type === 'nextScreen') {
                    let targetIndex = -1;
                    if (buttonAction.screenId) {
                      targetIndex = findScreenIndexById(
                        appSimulationScreens,
                        buttonAction.screenId,
                      );
                    } else {
                      targetIndex = !!appSimulationScreens?.[currentScreenIndex + 1]
                        ? currentScreenIndex + 1
                        : -1;
                    }
                    if (targetIndex === -1) continue;
                    setPreviousScreenIndex(currentScreenIndex);
                    setCurrentScreenIndex(targetIndex);
                    if (buttonAction.dataContextId && buttonAction.dataContext !== undefined) {
                      setDataContent((prev) => ({
                        ...prev,
                        [buttonAction.dataContextId!]: {
                          ...prev[buttonAction.dataContextId!],
                          value: buttonAction.dataContext,
                        },
                      }));

                      if (buttonAction.saveToSelections) {
                        setUserSelections((prev) => ({
                          ...prev,
                          [buttonAction.dataContextId!]: buttonAction.dataContext!,
                        }));
                      }
                    }
                  } else if (buttonAction.type === 'previousScreen') {
                    setCurrentScreenIndex(previousSreenIndex);
                    if (buttonAction.dataContextId && buttonAction.dataContext !== undefined) {
                      setDataContent((prev) => ({
                        ...prev,
                        [buttonAction.dataContextId!]: {
                          ...prev[buttonAction.dataContextId!],
                          value: buttonAction.dataContext,
                        },
                      }));

                      if (buttonAction.saveToSelections) {
                        setUserSelections((prev) => ({
                          ...prev,
                          [buttonAction.dataContextId!]: buttonAction.dataContext!,
                        }));
                      }
                    }
                  } else if (buttonAction.type === 'dropdown' && buttonAction.dropdownOptions) {
                    // Show dropdown
                    setDropdownOptions(buttonAction.dropdownOptions);
                    setDropdownPosition({
                      x: (element.x1 + element.x2) / 2,
                      y: element.y2 + 2,
                    });
                    setShowDropdown(true);
                  } else if (buttonAction.type === 'uploadPhoto') {
                    // Handle photo upload
                    handlePhotoUpload(buttonAction.dataContextId || 'image');
                  } else if (buttonAction.type === 'inputText') {
                    // Handle text input
                    handleTextInput(
                      buttonAction.inputTextType,
                      buttonAction.dataContextLabel,
                      buttonAction.dataContextId,
                      buttonAction.saveToSelections,
                    );
                  } else if (buttonAction.type === 'modal') {
                    // Handle dynamic modal
                    if (buttonAction.modalConfig) {
                      setCurrentModalConfig(buttonAction.modalConfig);
                      // Initialize modal values with current selections or defaults
                      const initialValues: Record<string, any> = {};
                      buttonAction.modalConfig.inputs.forEach((input) => {
                        // Generic initialization based on dataId
                        const currentValue = dynamicSelections[input.dataId!];
                        if (currentValue !== undefined) {
                          initialValues[input.id] = currentValue;
                        } else if (input.defaultValue !== undefined) {
                          initialValues[input.id] = input.defaultValue;
                        } else if (input.type === 'multiSelect') {
                          initialValues[input.id] = [];
                        } else if (
                          input.type === 'range' &&
                          input.min !== undefined &&
                          input.max !== undefined
                        ) {
                          initialValues[input.id] = [input.min, input.max];
                        } else {
                          initialValues[input.id] = '';
                        }
                      });
                      console.log('Modal initialized with values:', initialValues);
                      setModalValues(initialValues);
                      setShowModal(true);
                    }
                  } else if (buttonAction.type === 'image') {
                    if (buttonAction.dataContextId) {
                      setDataContent((prev) => ({
                        ...prev,
                        [buttonAction.dataContextId!]: {
                          ...prev[buttonAction.dataContextId!],
                          value: buttonAction.imgPath,
                        },
                      }));
                      if (buttonAction.saveToSelections) {
                        setUserSelections((prev) => ({
                          ...prev,
                          [buttonAction.dataContextId!]: buttonAction.dataContext!,
                        }));
                      }
                    }
                  } else if (buttonAction.type === 'checkbox') {
                    // Handle checkbox action
                    if (buttonAction.dataContextId && buttonAction.dataContext) {
                      handleCheckboxAction(
                        buttonAction.dataContextId,
                        buttonAction.dataContext,
                        buttonAction.dataContextLabel,
                        buttonAction.saveToSelections,
                      );
                    }
                  } else if (buttonAction.type === 'triggerMessage') {
                    // Handle trigger message
                    let finalMessage = buttonAction.message || '';

                    if (buttonAction.withData && Object.keys(userSelections).length > 0) {
                      const selectionsText = Object.entries(userSelections)
                        .map(([key, value]) => `${key}: ${value}`)
                        .join(', ');
                      finalMessage = `${finalMessage} ${selectionsText}`;
                    }

                    console.log('finalMessage ::: 2 ', finalMessage);

                    setJobsimulationTriggerMessage({
                      message: finalMessage,
                      isTriggered: true,
                    });
                  }
                }
              }}
              title={element.title}
            />
          ))}

          {/* Placeholders overlay */}
          {appSimulationScreens[currentScreenIndex].placeholders?.map((placeholder, index) => {
            // Get data content by dataId instead of screenId
            const currentContent = placeholder.dataId ? dataContent[placeholder.dataId] : undefined;

            // Check if this placeholder has time-based data
            const hasTimeBasedData = placeholder.dataByTime || placeholder.increaseByTime;
            const timeBasedValue = hasTimeBasedData
              ? (dataContent[placeholder.dataId || '']?.value ?? timeBasedValues[placeholder.id])
              : undefined;

            if (placeholder.type === 'image') {
              return (
                <PlaceholderImage
                  key={`placeholder-${placeholder.id || index}`}
                  placeholder={placeholder}
                  choosedImage={currentContent?.value}
                />
              );
            }

            if (placeholder.type === 'text') {
              // Determine what text to display
              let displayText = '';
              if (hasTimeBasedData && timeBasedValue) {
                displayText = timeBasedValue;
              } else if (placeholder.dataId && calculatedEstimates[placeholder.dataId]) {
                // Use calculated estimates for specific dataIds
                displayText = calculatedEstimates[placeholder.dataId];
              } else if (currentContent?.value) {
                displayText = currentContent.value;
              } else if (currentContent?.value === undefined && placeholder.initialValue) {
                displayText = placeholder.initialValue;
              }

              const renderedContent = renderPlaceholderContent(placeholder, displayText);

              return (
                <div
                  key={`placeholder-${placeholder.id || index}`}
                  style={getPlaceholderStyle(placeholder, { scaleFactor: imageScaleFactor })}
                >
                  {renderedContent}
                </div>
              );
            }

            if (placeholder.type === 'checkbox') {
              // Check if this checkbox value is selected
              const currentData = placeholder.dataId
                ? dataContent[placeholder.dataId]?.value
                : undefined;
              let isSelected = false;

              if (Array.isArray(currentData)) {
                isSelected = currentData.includes(placeholder.checkboxValue || '');
              } else if (typeof currentData === 'string' && currentData) {
                const currentArray = currentData
                  .split(',')
                  .map((item) => item.trim())
                  .filter(Boolean);
                isSelected = currentArray.includes(placeholder.checkboxValue || '');
              }

              return (
                <div
                  key={`placeholder-${placeholder.id || index}`}
                  style={getPlaceholderStyle(placeholder, {
                    isSelected,
                    scaleFactor: imageScaleFactor,
                  })}
                >
                  {isSelected ? '✓' : ''}
                </div>
              );
            }

            return null;
          })}

          {/* Charts overlay */}
          {appSimulationScreens[currentScreenIndex].charts?.map((chart, index) => {
            if (chart.type === 'line') {
              return <LineChart key={`chart-${chart.id || index}`} data={chart.data} />;
            }
            return null;
          })}

          {/* Dropdown overlay */}
          <AppSimulationDropdown
            dropdownOptions={dropdownOptions}
            handleClickOption={handleDropdownOptionClick}
            dropdownPosition={dropdownPosition}
            showDropdown={showDropdown}
          />
        </div>
      </div>

      {/* TODO: find a way to merge DialogTextInput with Dyanimic Modal --> Use Dynamic Modal */}
      <DialogTextInput
        isOpen={showTextDialog}
        onOpenChange={setShowTextDialog}
        textInputData={textInputData}
        onInputChange={(value) => setTextInputData((prev) => ({ ...prev, value }))}
        onSubmit={handleTextDialogSubmit}
      />

      {/* Dynamic Modal */}
      <DialogDynamicInputs
        open={showModal}
        onOpenChange={setShowModal}
        handleModalSubmit={handleModalSubmit}
        modalConfig={currentModalConfig}
        setModalValues={setModalValues}
        modalValues={modalValues}
      />
    </>
  );
};

export default AppSimulation;
