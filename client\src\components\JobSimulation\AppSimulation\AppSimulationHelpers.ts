import {
  AppSimulationFormatConfig,
  AppSimulationScreen,
  AppSimulationScreenElement,
  AppSimulationScreenPlaceholder,
} from '~/common';
import { CalculationConfig, CalculationEngine, formatRange } from './CalculationEngine';

// TODO: pass config as parameter
export const createCalculationEngine = (config: CalculationConfig): CalculationEngine | null => {
  try {
    if (!config) {
      console.warn(`No Config`);
      return null;
    }

    return new CalculationEngine(config);
  } catch (error) {
    console.error(`Error creating calculation engine ::: `, error);
    return null;
  }
};

export const safeCalculateEstimates = (
  engine: CalculationEngine | null,
  values: Record<string, any>,
): Record<string, any> => {
  if (!engine) {
    console.warn('No calculation engine available');
    return {};
  }

  try {
    // Check if we have at least some required inputs
    const requiredInputs = engine.getRequiredInputs();
    const hasAnyRequiredInput = requiredInputs.some((inputId) => values.hasOwnProperty(inputId));

    if (Object.keys(values).length === 0 && !hasAnyRequiredInput) {
      console.log('Skipping calculation: no values available');
      return {};
    }

    engine.updateValues(values);
    const estimates = engine.getAllValues();

    console.log('Calculation Engine - Updated values:', values);
    console.log('Calculation Engine - Results:', estimates);

    return estimates;
  } catch (error) {
    console.error('Error in calculation engine:', error);
    return {};
  }
};

export const hasTimeBasedPlaceholders = (screen: AppSimulationScreen): boolean => {
  return (
    screen.placeholders?.some(
      (placeholder) => placeholder.dataByTime || placeholder.increaseByTime,
    ) || false
  );
};

export const findScreenIndexById = (screens: AppSimulationScreen[], screenId: string) => {
  return screens.findIndex((screen) => screen.id === screenId);
};

// Get grid columns based on option count
export const getGridColumns = (count: number) => {
  if (count <= 4) return 'grid-cols-1';
  if (count <= 8) return 'grid-cols-2';
  return 'grid-cols-3';
};

export const formatValueWithType = (
  value: any,
  formatType?: string,
  formatConfig?: AppSimulationFormatConfig,
): string => {
  if (!formatType) {
    if (Array.isArray(value) && value.length >= 2) {
      return `${value[0]} - ${value[1]}`;
    }
    return String(value);
  }

  switch (formatType) {
    case 'age-range':
      if (Array.isArray(value) && value.length >= 2) {
        return formatRange([value[0], value[1]], {
          maxValue: formatConfig?.maxValue || 65,
          maxLabel: formatConfig?.maxLabel || '65+',
          separator: formatConfig?.separator,
        });
      }
      break;
    case 'range':
      if (Array.isArray(value) && value.length >= 2) {
        return formatRange([value[0], value[1]], formatConfig);
      }
      break;
    case 'currency':
      if (typeof value === 'number') {
        return `$${value.toFixed(2)} USD`;
      }
      break;
    case 'percentage':
      if (typeof value === 'number') {
        return `${value}%`;
      }
      break;
    default:
      if (Array.isArray(value) && value.length >= 2) {
        return `${value[0]} - ${value[1]}`;
      }
      return String(value);
  }

  return String(value);
};

export const uploadAppSimulationPhoto = (inputs: {
  callbackImage?: (imgUrl) => void;
  width?: number;
  height?: number;
}) => {
  const { callbackImage, width = 240, height = 240 } = inputs;
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/*';
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = (event) => {
      const imageUrl = event.target?.result as string;
      // TODO: Crop image to 240x240 ??? need to pass as parameter
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = width;
        canvas.height = height;

        if (!ctx) return;
        // Calculate crop dimensions to maintain aspect ratio
        const size = Math.min(img.width, img.height);
        const x = (img.width - size) / 2;
        const y = (img.height - size) / 2;

        ctx.drawImage(img, x, y, size, size, 0, 0, width, height);
        const croppedImageUrl = canvas.toDataURL();

        // Call the callback function
        callbackImage?.(croppedImageUrl);
      };
      img.src = imageUrl;
    };
    reader.readAsDataURL(file);
  };
  input.click();
};

export const getElementStyles = (
  element: AppSimulationScreenElement & { index: number },
  options?: any,
): React.CSSProperties => {
  return {
    position: 'absolute',
    left: `${element.x1}%`,
    top: `${element.y1}%`,
    width: `${element.x2 - element.x1}%`,
    height: `${element.y2 - element.y1}%`,
    cursor: 'pointer',
    zIndex: 16,
    backgroundColor: !!options?.isHovered
      ? (element.backgroundColor ?? 'rgba(0, 123, 255, 0.2)')
      : 'transparent',
    border: !!options?.isHovered ? (element.border ?? '2px solid rgba(0, 123, 255, 0.5)') : 'none',
    borderRadius: '4px',
    transition: 'all 0.2s ease',
    pointerEvents: 'auto',
  };
};

export const getPlaceholderStyle = (
  placeholder: AppSimulationScreenPlaceholder,
  options?: any,
): React.CSSProperties => {
  const generalPlaceholderStyle = {
    position: 'absolute',
    left: `${placeholder.x1}%`,
    top: `${placeholder.y1}%`,
    width: `${placeholder.x2 - placeholder.x1}%`,
    height: `${placeholder.y2 - placeholder.y1}%`,
    zIndex: 15,
  };
  let defaultPlaceholderStyle = {};
  if (placeholder.type === 'text') {
    const isTagDisplay = placeholder.dataDisplayType === 'tag';
    const hasTimeBasedData = placeholder.dataByTime || placeholder.increaseByTime;
    defaultPlaceholderStyle = {
      display: 'flex',
      justifyContent: isTagDisplay ? 'flex-start' : 'left',
      alignItems: isTagDisplay ? 'flex-start' : 'center',
      backgroundColor: hasTimeBasedData ? 'rgba(59, 130, 246, 0.1)' : 'rgba(255, 255, 255, 1)',
      borderRadius: '4px',
      fontSize: isTagDisplay ? undefined : '0.75rem',
      lineHeight: isTagDisplay ? undefined : '0.75rem',
      padding: isTagDisplay ? '2px' : '2px',
      color: hasTimeBasedData ? '#1e40af' : '#333',
      textAlign: 'left',
      overflow: 'auto',
      wordBreak: 'break-word',
      whiteSpace: isTagDisplay ? 'normal' : 'pre-line',
      fontWeight: hasTimeBasedData ? 'bold' : 'normal',
      border: hasTimeBasedData ? '1px solid rgba(59, 130, 246, 0.3)' : 'none',
    };
  } else if (placeholder.type === 'image') {
    defaultPlaceholderStyle = {
      overflow: 'hidden',
      borderRadius: '4px',
    };
  } else if (placeholder.type === 'checkbox') {
    defaultPlaceholderStyle = {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      fontSize: '0.875rem',
      fontWeight: 'bold',
      color: !!options?.isSelected ? '#059669' : 'transparent',
      pointerEvents: 'none',
    };
  }

  return Object.assign(generalPlaceholderStyle, defaultPlaceholderStyle, placeholder.style || {});
};

export const getDropdownStyle = (dropdownPosition: {
  x: number;
  y: number;
}): React.CSSProperties => {
  return {
    position: 'absolute',
    left: `${dropdownPosition.x}%`,
    top: `${dropdownPosition.y}%`,
    transform: 'translateX(-50%)',
    zIndex: 20,
    backgroundColor: 'white',
    border: '1px solid #ccc',
    borderRadius: '4px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
    minWidth: '150px',
  };
};
